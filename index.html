<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rhythm Pose - 动作识别系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <!-- 移除 TensorFlow.js 库以避免与 ml5.js 冲突 -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/pose-detection@2.0.0/dist/pose-detection.min.js"></script> -->
    <!-- p5.js 库 (ml5.js 依赖) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <!-- ml5.js 库 - 使用稳定版本 -->
    <script src="https://unpkg.com/ml5@0.12.2/dist/ml5.min.js"></script>
    <!-- MediaPipe Hands (使用更稳定的版本) -->
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands@0.4.1646424915/hands.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils@0.3.1640029074/camera_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils@0.3.1620248257/drawing_utils.js"></script>
    <!-- Dependencies for zkTLS -->
    <script src="https://cdn.jsdelivr.net/npm/ethers@5.7.2/dist/ethers.umd.min.js"></script>
    <!-- zkTLS Browser Wrapper -->
    <script src="js/zktls-browser-wrapper.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎯 Rhythm Pose</h1>
            <p>AI 动作识别与评分系统</p>
        </header>

        <main class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="status-info">
                    <div class="status-item">
                        <span class="label">摄像头状态:</span>
                        <span id="camera-status" class="status">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="label">AI 模型:</span>
                        <span id="model-status" class="status">加载中...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">检测状态:</span>
                        <span id="detection-status" class="status">待机</span>
                    </div>
                </div>

                <div class="action-controls">
                    <button id="camera-btn" class="btn btn-outline">启用摄像头</button>
                    <button id="start-btn" class="btn btn-primary" disabled>开始检测</button>
                    <button id="stop-btn" class="btn btn-secondary" disabled>停止检测</button>
                    <button id="reset-btn" class="btn btn-outline">重置</button>
                </div>

                <div class="display-controls">
                    <label class="toggle-container">
                        <input type="checkbox" id="show-skeleton" checked>
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">显示骨骼和关节点</span>
                    </label>


                </div>

                <div class="detection-mode">
                    <label for="mode-select">检测模式:</label>
                    <select id="mode-select" class="select">
                        <option value="pose">人体姿势</option>
                        <option value="hands">手部动作</option>
                    </select>
                </div>

                <div class="pose-selector">
                    <label for="pose-select">选择动作:</label>
                    <select id="pose-select" class="select">
                        <!-- 动作选项将由JavaScript动态生成 -->
                    </select>
                </div>

                <div class="custom-actions">
                    <h4>自定义动作</h4>
                    <div class="custom-action-controls">
                        <button id="create-action-btn" class="btn btn-outline">➕ 创建动作</button>
                        <button id="manage-actions-btn" class="btn btn-outline">⚙️ 管理动作</button>
                    </div>
                    <div id="custom-actions-list" class="custom-actions-list">
                        <p class="no-custom-actions">暂无自定义动作</p>
                    </div>
                </div>

                <!-- zkTLS 证明系统 -->
                <div class="zktls-section">
                    <h4>🔐 zkTLS 证明系统</h4>
                    <div class="zktls-status">
                        <div class="status-item">
                            <span class="label">zkTLS状态:</span>
                            <span id="zktls-status" class="status">未初始化</span>
                        </div>
                        <div class="status-item">
                            <span class="label">用户地址:</span>
                            <span id="user-address" class="status">未连接</span>
                        </div>
                    </div>

                    <div class="zktls-controls">
                        <button id="connect-wallet-btn" class="btn btn-outline">🔗 连接钱包</button>
                        <button id="init-zktls-btn" class="btn btn-outline" disabled>🚀 初始化zkTLS</button>
                        <button id="generate-proof-btn" class="btn btn-primary" disabled>📜 生成证明</button>
                    </div>

                    <div class="proof-display">
                        <h5>最新证明</h5>
                        <div id="latest-proof" class="proof-info">
                            <p class="no-proof">暂无证明</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频显示区域 -->
            <div class="video-container">
                <div id="video-wrapper">
                    <!-- p5.js canvas 将在这里创建 -->
                </div>
                
                <!-- 姿态信息显示 -->
                <div class="pose-info">
                    <div class="pose-guide">
                        <h3>动作指导</h3>
                        <div id="pose-instructions">
                            请选择一个动作开始练习
                        </div>
                    </div>
                    
                    <div class="pose-feedback">
                        <h3>实时反馈</h3>
                        <div id="pose-feedback">
                            等待检测...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评分区域 -->
            <div class="scoring-panel">
                <div class="score-display">
                    <div class="current-score">
                        <span class="score-label">当前得分</span>
                        <span id="current-score" class="score-value">0</span>
                    </div>
                    <div class="best-score">
                        <span class="score-label">最佳得分</span>
                        <span id="best-score" class="score-value">0</span>
                    </div>
                </div>

                <!-- 游戏化元素 -->
                <div class="game-stats">
                    <div class="level-display">
                        <span class="level-label">等级 <span id="player-level">1</span></span>
                        <div class="level-progress">
                            <div id="level-progress-bar" class="progress-bar"></div>
                        </div>
                    </div>

                    <div class="combo-display">
                        <span id="combo-text" class="combo-text"></span>
                        <span class="combo-count">连击: <span id="combo-count">0</span></span>
                    </div>

                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-label">总分:</span>
                            <span id="total-score" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">完美:</span>
                            <span id="perfect-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">良好:</span>
                            <span id="good-count" class="stat-value">0</span>
                        </div>
                    </div>
                </div>

                <div class="score-breakdown">
                    <h4>评分详情</h4>
                    <div class="score-metrics">
                        <div class="metric">
                            <span class="metric-name">姿态准确度:</span>
                            <span id="accuracy-score" class="metric-value">0%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">稳定性:</span>
                            <span id="stability-score" class="metric-value">0%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">持续时间:</span>
                            <span id="duration-score" class="metric-value">0s</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">奖励倍数:</span>
                            <span id="streak-bonus" class="metric-value">1.0x</span>
                        </div>
                    </div>
                </div>

                <!-- 成就显示 -->
                <div class="achievements-panel">
                    <h4>🏆 最新成就</h4>
                    <div id="achievements-list" class="achievements-list">
                        <span class="no-achievements">暂无成就</span>
                    </div>
                </div>

                <!-- zkTLS 证明历史 -->
                <div class="proof-history-panel">
                    <h4>🔐 证明历史</h4>
                    <div class="proof-stats">
                        <div class="stat-item">
                            <span class="stat-label">总证明数:</span>
                            <span id="total-proofs" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已验证:</span>
                            <span id="verified-proofs" class="stat-value">0</span>
                        </div>
                    </div>
                    <div id="proof-history-list" class="proof-history-list">
                        <span class="no-proofs">暂无证明记录</span>
                    </div>
                    <div class="proof-actions">
                        <button id="export-proofs-btn" class="btn btn-outline btn-small">📤 导出证明</button>
                        <button id="clear-proofs-btn" class="btn btn-outline btn-small">🗑️ 清除历史</button>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>基于 ml5.js 构建 | 支持实时动作识别与评分</p>
        </footer>
    </div>

    <!-- JavaScript 文件 -->
    <script src="js/zktls-config.js"></script>
    <script src="js/zktls-integration.js"></script>
    <script src="js/zktls-scoring-integration.js"></script>
    <script src="js/pose-definitions.js"></script>
    <script src="js/pose-detector.js"></script>
    <script src="js/hand-detector.js"></script>
    <script src="js/mediapipe-hand-detector.js"></script>
    <script src="js/scoring-system.js"></script>
    <script src="js/custom-action-manager.js"></script>
    <script src="js/image-processor.js"></script>
    <script src="js/keypoint-editor.js"></script>
    <script src="js/visual-feedback.js"></script>
    <script src="js/custom-action-creator.js"></script>
    <script src="js/custom-action-manager-ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
